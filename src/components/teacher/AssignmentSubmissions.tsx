import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getAssignmentById,
  getAssignmentSubmissions,
  gradeSubmission,
  AssignmentSubmission
} from '../../api/assignments';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  ArrowLeft,
  FileText,
  Download,
  CheckCircle2,
  AlertCircle,
  Loader2,
  User,
  Calendar,
  Mail
} from 'lucide-react';

interface GradeFormData {
  grade: string;
  feedback: string;
}

const AssignmentSubmissions = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [selectedSubmission, setSelectedSubmission] = useState<AssignmentSubmission | null>(null);
  const [isGradeDialogOpen, setIsGradeDialogOpen] = useState(false);
  const [gradeForm, setGradeForm] = useState<GradeFormData>({
    grade: '',
    feedback: ''
  });

  // Fetch assignment details
  const { 
    data: assignment, 
    isLoading: isLoadingAssignment,
    error: assignmentError
  } = useQuery({
    queryKey: ['assignment', id],
    queryFn: () => id ? getAssignmentById(id) : Promise.resolve(null),
    enabled: !!id
  });

  // Fetch submissions for this assignment
  const {
    data: submissions = [],
    isLoading: isLoadingSubmissions
  } = useQuery({
    queryKey: ['assignmentSubmissions', id],
    queryFn: () => id ? getAssignmentSubmissions(id) : Promise.resolve([]),
    enabled: !!id
  });

  // Mutation for grading a submission
  const gradeMutation = useMutation({
    mutationFn: ({ submissionId, grade, feedback }: { submissionId: string; grade: number; feedback?: string }) =>
      gradeSubmission(submissionId, { grade, feedback }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['assignmentSubmissions', id] });
      setIsGradeDialogOpen(false);
      setSelectedSubmission(null);
      resetGradeForm();
      toast.success('Submission graded successfully');
    },
    onError: (error: Error) => {
      toast.error(error.message || 'Failed to grade submission');
    }
  });

  const resetGradeForm = () => {
    setGradeForm({
      grade: '',
      feedback: ''
    });
  };

  const openGradeDialog = (submission: AssignmentSubmission) => {
    setSelectedSubmission(submission);
    setGradeForm({
      grade: submission.grade?.toString() || '',
      feedback: submission.feedback || ''
    });
    setIsGradeDialogOpen(true);
  };

  const handleGradeSubmission = () => {
    if (!selectedSubmission) return;

    const grade = parseInt(gradeForm.grade);
    const maxPoints = assignment?.points || 100;
    if (isNaN(grade) || grade < 0 || grade > maxPoints) {
      toast.error(`Grade must be a number between 0 and ${maxPoints}`);
      return;
    }

    gradeMutation.mutate({
      submissionId: selectedSubmission.id,
      grade,
      feedback: gradeForm.feedback
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "submitted":
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          <CheckCircle2 className="h-4 w-4 mr-1" />Submitted
        </Badge>;
      case "graded":
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
          <CheckCircle2 className="h-4 w-4 mr-1" />Graded
        </Badge>;
      case "late":
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
          <AlertCircle className="h-4 w-4 mr-1" />Late
        </Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  if (isLoadingAssignment || isLoadingSubmissions) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (assignmentError || !assignment) {
    return (
      <div className="p-4 bg-red-50 text-red-700 rounded-md">
        <p className="font-semibold">Error loading assignment</p>
        <p>{(assignmentError as Error)?.message || "Assignment not found"}</p>
        <Button 
          variant="outline" 
          className="mt-4"
          onClick={() => navigate('/dashboard/teacher/assignments')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Assignments
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Button 
          variant="outline" 
          onClick={() => navigate('/dashboard/teacher/assignments')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Assignments
        </Button>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            {assignment.title} - Submissions
          </CardTitle>
          <CardDescription>
            Due: {formatDate(assignment.due_date)} • Points: {assignment.points}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="prose max-w-none mb-6">
            <p>{assignment.description}</p>
          </div>
          
          <Separator className="my-4" />
          
          <h3 className="text-lg font-semibold mb-4">Student Submissions ({submissions.length})</h3>
          
          {submissions.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
              <p>No submissions yet</p>
            </div>
          ) : (
            <div className="space-y-4">
              {submissions.map((submission: AssignmentSubmission) => (
                <Card key={submission.id} className="overflow-hidden">
                  <CardContent className="p-0">
                    <div className="flex flex-col md:flex-row">
                      <div className="flex-1 p-6">
                        <div className="flex items-start justify-between">
                          <div>
                            <div className="flex items-center mb-2">
                              <User className="h-4 w-4 mr-2 text-muted-foreground" />
                              <span className="font-medium">{submission.student?.name}</span>
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground mb-2">
                              <Mail className="h-4 w-4 mr-2" />
                              <span>{submission.student?.email}</span>
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Calendar className="h-4 w-4 mr-2" />
                              <span>Submitted: {formatDate(submission.submitted_at)}</span>
                            </div>
                          </div>
                          <div>{getStatusBadge(submission.status)}</div>
                        </div>
                        
                        {submission.content && (
                          <div className="mt-4">
                            <h4 className="text-sm font-medium mb-1">Comments</h4>
                            <div className="bg-muted p-3 rounded-md">
                              <p>{submission.content}</p>
                            </div>
                          </div>
                        )}
                        
                        {submission.file_url && (
                          <div className="mt-4">
                            <h4 className="text-sm font-medium mb-1">Attachment</h4>
                            <div className="flex items-center justify-between bg-muted p-3 rounded-md">
                              <div className="flex items-center">
                                <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                                <span>{submission.file_name}</span>
                              </div>
                              <Button variant="ghost" size="sm" asChild>
                                <a href={submission.file_url} target="_blank" rel="noopener noreferrer">
                                  <Download className="h-4 w-4 mr-1" />
                                  Download
                                </a>
                              </Button>
                            </div>
                          </div>
                        )}
                        
                        {submission.status === 'graded' && (
                          <div className="mt-4 pt-4 border-t">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-medium">Grade</h4>
                              <span className="font-medium">{submission.grade} / {assignment.points}</span>
                            </div>
                            
                            {submission.feedback && (
                              <div>
                                <h4 className="text-sm font-medium mb-1">Feedback</h4>
                                <div className="bg-muted p-3 rounded-md">
                                  <p>{submission.feedback}</p>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                      
                      <div className="bg-muted p-6 flex flex-col justify-center items-center md:w-48">
                        <Button 
                          onClick={() => openGradeDialog(submission)}
                          className="w-full"
                          variant={submission.status === 'graded' ? 'outline' : 'default'}
                        >
                          {submission.status === 'graded' ? 'Update Grade' : 'Grade'}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Grade Submission Dialog */}
      <Dialog open={isGradeDialogOpen} onOpenChange={setIsGradeDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Grade Submission</DialogTitle>
            <DialogDescription>
              {selectedSubmission?.student?.name}'s submission for {assignment.title}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="grade" className="text-right">
                Grade (out of {assignment.points})
              </Label>
              <Input
                id="grade"
                type="number"
                min="0"
                max={assignment.points}
                value={gradeForm.grade}
                onChange={(e) => setGradeForm(prev => ({ ...prev, grade: e.target.value }))}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="feedback" className="text-right">
                Feedback
              </Label>
              <Textarea
                id="feedback"
                value={gradeForm.feedback}
                onChange={(e) => setGradeForm(prev => ({ ...prev, feedback: e.target.value }))}
                className="col-span-3"
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsGradeDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleGradeSubmission}
              disabled={gradeMutation.isPending}
            >
              {gradeMutation.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Save Grade'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AssignmentSubmissions; 